//
//  PageHeaderView.swift
//  DouYinSwift5
//
//  Created by lym on 2020/7/28.
//  Copyright © 2020 lym. All rights reserved.
//

import UIKit

// 定义分段视图的高度常量
var segmentViewHeight: CGFloat { return 46 }

class PageHeaderView: UICollectionReusableView, UITextViewDelegate {
    // MARK: - UI组件属性
    
    private var contentCardView: UIView!
    
    // 背景图片视图 - 可以替换为Personal中的背景图
    private var headerImage: UIImageView!
    
    // 用户头像 - 可以增加渐变边框效果
    private var avatar: UIImageView!
    
    // 内容背景容器 - 可以设置为白色或浅灰色背景
    private var bgContainerView: UIView!

    // 按钮容器 - 包含关注、私信等按钮
    private var btnStack: UIStackView!
    
    // 推荐按钮 - 在新UI中可能不需要
//    private var recommendBtn: UIButton!
    
    // 关注按钮 - 可以设置渐变背景
    private var followBtn: UIButton!
    
    // 私信按钮 - 可以设置为白底黑字
    private var sendMsgBtn: UIButton!

    // 用户名称和描述的垂直堆栈视图
    private var nickNameStack: UIStackView!
    
    // 分隔线 - 可以设置为浅灰色
//    private var splitLine: UIView!
    
    // 用户描述标签 - 可以使用新UI中的样式
    private var userDesc: UILabel!
    
    // 用户信息标签堆栈 - 年龄、位置等
//    private var userInfoTagStack: UIStackView!
    
    // 统计数据堆栈 - 关注数、粉丝数等
    private var statisticStack: StatsView!
    
    // 分段视图 - 用于分类展示内容
    public var segmentView: PageSegmentView!

    private var overlayView: UIView?
    private var gradientLayer: CAGradientLayer?

    // 新增：保存UI引用，便于数据绑定
    private var nickNameLabel: UILabel?
    private var accountLabel: UILabel?
    private var signLabel: UITextView?
    private var tagListStack: UIView?
    private var followingStatsView: StatsView?
    private var likesStatsView: StatsView?
    private var followersStatsView: StatsView?

    var onFollowButtonTap: ((_ isFollowing: Bool) -> Void)?

    override init(frame: CGRect) {
        super.init(frame: frame)
        setUpUI()
    }

    /// 计算PageHeaderView所需的动态高度
    /// - Parameter containerWidth: 容器宽度
    /// - Returns: 计算出的高度
    func calculateRequiredHeight(for containerWidth: CGFloat) -> CGFloat {
        // 基础高度组成：
        // 1. 背景图片区域：146
        // 2. 头像：80 + 8（间距）
        // 3. 昵称：18 + 4（间距）
        // 4. 账号：12 + 4（间距）
        // 5. 标签：20 + 8（间距）
        // 6. 个性签名：动态计算 + 12（间距）
        // 7. 统计信息：50 + 16（间距）
        // 8. 按钮：40 + 16（间距）
        // 9. 到segmentView的间距：20
        // 10. segmentView：DefaultSegmentViewHeight

        let baseHeight: CGFloat = 146 + 80 + 8 + 18 + 4 + 12 + 4 + 20 + 8 + 12 + 50 + 16 + 40 + 16 + 20 + DefaultSegmentViewHeight

        // 计算个性签名的高度（如果有的话）
        var signatureHeight: CGFloat = 12 // 默认最小高度，即使没有文本也要有基本间距
        if let signLabel = signLabel, let text = signLabel.text, !text.isEmpty {
            let signatureWidth = containerWidth - 46 * 2 // 左右边距
            let font = UIFont.systemFont(ofSize: 12)
            let boundingRect = text.boundingRect(
                with: CGSize(width: signatureWidth, height: CGFloat.greatestFiniteMagnitude),
                options: [.usesLineFragmentOrigin, .usesFontLeading],
                attributes: [.font: font],
                context: nil
            )
            signatureHeight = max(12, ceil(boundingRect.height)) // 确保最小高度
        }

        let totalHeight = baseHeight + signatureHeight
        print("计算PageHeaderView高度: baseHeight=\(baseHeight), signatureHeight=\(signatureHeight), totalHeight=\(totalHeight)")
        return totalHeight
    }

    @available(*, unavailable)
    required init?(coder _: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    override func prepareForReuse() {
        super.prepareForReuse()
    }

    override var reuseIdentifier: String? {
        return PageHeaderView.description()
    }
}

// MARK: - UI 相关方法

extension PageHeaderView {
    // 按顺序设置所有UI组件
    fileprivate func setUpUI() {
        
        self.backgroundColor = UIColor(hex: "#F5F5F5")
        
        // 添加背景图片
        addBackgroundImage()
        
        // 添加内容背景容器
        addBgContainerView()
        
        addContentCardView()
        
        // 添加头像
        // 【现代UI】: 可以添加渐变边框，使用额外的视图包裹avatar
        addAvatarBtn()

        // 添加按钮容器
        // 【现代UI】: 可以放在内容卡片底部，两个主要按钮并排
        addBtnStack()
        
        // 添加关注按钮
        // 【现代UI】: 可以设置为渐变背景，圆角20
        addFollowBtn()
        
        // 添加私信按钮
        // 【现代UI】: 可以设置为白底黑字，带灰色边框
        addSendMessageBtn()
        
        // 添加推荐按钮
        // 【现代UI】: 可能不需要此按钮
//        addRecommendBtn()

        // 添加昵称和描述堆栈
        // 【现代UI】: 可以居中显示，字体颜色改为黑色
        addNickNameAndDesc()
        
        // 添加分隔线
        // 【现代UI】: 可以调整颜色为浅灰色
//        addSplitLine()
        
        // 添加统计信息
        // 【现代UI】: 可以调整为居中显示的三列
        addStatisticInfo()
        
        // 添加分段视图
        // 【现代UI】: 已经实现了圆角，保持不变
        addSegmentView()
    }

    // 添加背景图片
    // 【现代UI】: 可以添加渐变蒙层，使底部与内容过渡更自然
    private func addBackgroundImage() {
        headerImage = UIImageView()
        headerImage.image = UIImage(named: "default_avatar_test")
        headerImage.contentMode = .scaleAspectFill
        addSubview(headerImage)
        headerImage.translatesAutoresizingMaskIntoConstraints = false
        headerImage.leftAnchor.constraint(equalTo: leftAnchor).isActive = true
        headerImage.topAnchor.constraint(equalTo: topAnchor).isActive = true
        headerImage.rightAnchor.constraint(equalTo: rightAnchor).isActive = true
        headerImage.bottomAnchor.constraint(equalTo: bottomAnchor, constant: -91).isActive = true
        
        // 添加渐变遮罩层 - 作为headerImage的子视图，确保它跟随headerImage的变换
        let overlayView = UIView()
        // 该视图仅用于渲染渐变，不需要响应触摸，关闭交互以避免遮挡下方按钮
        overlayView.isUserInteractionEnabled = false
        overlayView.backgroundColor = .clear
        headerImage.addSubview(overlayView)
        
        // 设置遮罩层填满headerImage
        overlayView.translatesAutoresizingMaskIntoConstraints = false
        overlayView.leftAnchor.constraint(equalTo: headerImage.leftAnchor).isActive = true
        overlayView.topAnchor.constraint(equalTo: headerImage.topAnchor, constant: -10).isActive = true
        overlayView.rightAnchor.constraint(equalTo: headerImage.rightAnchor).isActive = true
        overlayView.bottomAnchor.constraint(equalTo: headerImage.bottomAnchor, constant: 10).isActive = true
        
        // 创建渐变层
        let gradientLayer = CAGradientLayer()
        gradientLayer.colors = [
            UIColor.white.withAlphaComponent(0.2).cgColor,
            UIColor(hex: "#F5F5F5").cgColor
        ]
        gradientLayer.locations = [0.0, 1.0]
        gradientLayer.startPoint = CGPoint(x: 0.5, y: 0.0)
        gradientLayer.endPoint = CGPoint(x: 0.5, y: 1.0)
        
        // 设置渐变层填满遮罩视图
        DispatchQueue.main.async {
            gradientLayer.frame = overlayView.bounds
            overlayView.layer.insertSublayer(gradientLayer, at: 0)
        }
    }

    // 添加内容背景容器
    private func addBgContainerView() {
        bgContainerView = UIView()
//        bgContainerView.backgroundColor = UIColor(hex: "#F5F5F5", alpha: 0.1)
        bgContainerView.backgroundColor = .clear
        addSubview(bgContainerView)
        bgContainerView.translatesAutoresizingMaskIntoConstraints = false
        bgContainerView.leftAnchor.constraint(equalTo: leftAnchor).isActive = true
        bgContainerView.topAnchor.constraint(equalTo: topAnchor).isActive = true
        bgContainerView.rightAnchor.constraint(equalTo: rightAnchor).isActive = true
        bgContainerView.bottomAnchor.constraint(equalTo: bottomAnchor).isActive = true
    }
    
    // 【现代UI】: 可以改为浅灰色或白色，例如UIColor(hex: "#F5F5F5")
    private func addContentCardView() {
        // 【现代UI】: 可以在这里添加内容卡片视图
        contentCardView = UIView()
        contentCardView.backgroundColor = UIColor(hex: "#FFFFFF", alpha: 0.93)
        contentCardView.layer.cornerRadius = 12
        contentCardView.layer.shadowColor = UIColor.black.withAlphaComponent(0.1).cgColor
        contentCardView.layer.shadowOffset = CGSize(width: 0, height: 2)
        contentCardView.layer.shadowRadius = 10
        addSubview(contentCardView)
        
        contentCardView.translatesAutoresizingMaskIntoConstraints = false
        contentCardView.leftAnchor.constraint(equalTo: leftAnchor, constant: 19).isActive = true
        contentCardView.topAnchor.constraint(equalTo: headerImage.topAnchor, constant: 146).isActive = true
        contentCardView.rightAnchor.constraint(equalTo: rightAnchor, constant: -19).isActive = true
        // 移除固定的底部约束，让内容决定高度
        // contentCardView.bottomAnchor.constraint(equalTo: bottomAnchor, constant: -77).isActive = true
    }

    // 添加头像
    // 【现代UI】: 可以添加渐变边框效果
    private func addAvatarBtn() {
        // 创建头像渐变边框容器
        let avatarContainer = UIView()
        avatarContainer.backgroundColor = .clear
        contentCardView.addSubview(avatarContainer)
        
        // 设置头像容器位置
        avatarContainer.translatesAutoresizingMaskIntoConstraints = false
        avatarContainer.centerXAnchor.constraint(equalTo: contentCardView.centerXAnchor).isActive = true
        avatarContainer.topAnchor.constraint(equalTo: contentCardView.topAnchor, constant: -38).isActive = true
        avatarContainer.widthAnchor.constraint(equalToConstant: 83).isActive = true
        avatarContainer.heightAnchor.constraint(equalToConstant: 83).isActive = true
        
        // 添加渐变边框层
        let gradientLayer = CAGradientLayer()
        gradientLayer.colors = [
            UIColor(hex: "#FF8D36").cgColor,
            UIColor(hex: "#FF6236").cgColor,
            UIColor(hex: "#FF3B74").cgColor
        ]
        gradientLayer.startPoint = CGPoint(x: 0, y: 0)
        gradientLayer.endPoint = CGPoint(x: 1, y: 1)
        gradientLayer.locations = [0, 0.5, 1]
        gradientLayer.cornerRadius = 41.5
        gradientLayer.frame = CGRect(x: 0, y: 0, width: 83, height: 83)
        avatarContainer.layer.addSublayer(gradientLayer)
        
        // 创建头像视图
        avatar = UIImageView()
        avatar.cornerRadius = 38
        avatar.clipsToBounds = true
        avatar.contentMode = .scaleAspectFill
        avatar.image = UIImage(named: "default_avatar_test")
        
        // 在渐变容器中央添加头像
        avatarContainer.addSubview(avatar)
        avatar.translatesAutoresizingMaskIntoConstraints = false
        avatar.centerXAnchor.constraint(equalTo: avatarContainer.centerXAnchor).isActive = true
        avatar.centerYAnchor.constraint(equalTo: avatarContainer.centerYAnchor).isActive = true
        avatar.widthAnchor.constraint(equalToConstant: 77).isActive = true
        avatar.heightAnchor.constraint(equalToConstant: 77).isActive = true
    }

    // 添加按钮容器
    // 【现代UI】: 可以放在内容卡片底部
    private func addBtnStack() {
        // 此方法不再需要，因为按钮已在addStatisticInfo中创建
        // 但保留空方法以避免修改其他逻辑
    }

    // 添加关注按钮
    // 【现代UI】: 可以设置渐变背景和更大的圆角
    private func addFollowBtn() {
        // 此方法不再需要，因为按钮已在addStatisticInfo中创建
        // 但保留空方法以避免修改其他逻辑
    }

    // 添加私信按钮
    // 【现代UI】: 可以设置为白底黑字
    private func addSendMessageBtn() {
        // 此方法不再需要，因为按钮已在addStatisticInfo中创建
        // 但保留空方法以避免修改其他逻辑
    }

    // 添加推荐按钮
    // 【现代UI】: 在新设计中可能不需要
//    private func addRecommendBtn() {
//        recommendBtn = UIButton()
//        recommendBtn.widthAnchor.constraint(equalToConstant: 40).isActive = true
//        recommendBtn.heightAnchor.constraint(equalToConstant: 40).isActive = true
//        recommendBtn.cornerRadius = 2.5
//        btnStack.addArrangedSubview(recommendBtn)
//
//        // 【现代UI】: 可以考虑移除此按钮
//    }

    // 添加昵称和描述堆栈
    // 【现代UI】: 可以居中显示
    private func addNickNameAndDesc() {
        nickNameStack = UIStackView()
        nickNameStack.alignment = .fill
        nickNameStack.axis = .vertical
        nickNameStack.distribution = .fill // 改为fill，让子视图根据内容自适应高度
        nickNameStack.spacing = 4
        contentCardView.addSubview(nickNameStack)
        nickNameStack.alignment = .center
        
        nickNameStack.translatesAutoresizingMaskIntoConstraints = false
        nickNameStack.leftAnchor.constraint(equalTo: contentCardView.leftAnchor, constant: 46).isActive = true
        nickNameStack.topAnchor.constraint(equalTo: avatar.bottomAnchor, constant: 8).isActive = true
        nickNameStack.rightAnchor.constraint(equalTo: contentCardView.rightAnchor, constant: -46).isActive = true
        
        // 昵称
        let nickName = UILabel()
        nickName.text = "用户名用户名"
        nickName.font = .boldSystemFont(ofSize: 18)
        nickName.textColor = UIColor(hex: "#333333")
        nickNameStack.addArrangedSubview(nickName)
        self.nickNameLabel = nickName
        
        // 树小柒号
        let sxqID = UILabel()
        sxqID.text = "树小柒号：**********"
        sxqID.font = .systemFont(ofSize: 12)
        sxqID.textColor = UIColor(hex: "#444444")
        nickNameStack.addArrangedSubview(sxqID)
        self.accountLabel = sxqID
        
        // 创建标签容器 - 改为简单的水平StackView
        let tagsContainerView = UIStackView()
        tagsContainerView.axis = .horizontal
        tagsContainerView.spacing = 8
        tagsContainerView.alignment = .center
        tagsContainerView.distribution = .equalSpacing
        tagsContainerView.heightAnchor.constraint(greaterThanOrEqualToConstant: 20).isActive = true
        nickNameStack.addArrangedSubview(tagsContainerView)
        self.tagListStack = tagsContainerView

        // 个性签名 - 独立添加，不放在StackView中，使用UITextView支持链接点击
        let signTextView = UITextView()
        signTextView.text = "个性签名个性签名个性签名个性签名个性签名"
        signTextView.font = .systemFont(ofSize: 12)
        signTextView.textColor = UIColor(hex: "#777777")
        signTextView.textAlignment = .center
        signTextView.isEditable = false
        signTextView.isScrollEnabled = false
        signTextView.backgroundColor = .clear
        signTextView.textContainerInset = .zero
        signTextView.textContainer.lineFragmentPadding = 0
        signTextView.textContainer.maximumNumberOfLines = 0 // 设置为0表示无行数限制，允许完全展示
        signTextView.textContainer.lineBreakMode = .byWordWrapping // 确保按单词换行
        signTextView.textContainer.widthTracksTextView = true // 确保宽度跟随文本视图
        signTextView.delegate = self

        // 直接添加到contentCardView，不放在StackView中
        contentCardView.addSubview(signTextView)
        signTextView.translatesAutoresizingMaskIntoConstraints = false

        // 设置独立约束
        signTextView.leftAnchor.constraint(equalTo: contentCardView.leftAnchor, constant: 46).isActive = true
        signTextView.rightAnchor.constraint(equalTo: contentCardView.rightAnchor, constant: -46).isActive = true
        signTextView.topAnchor.constraint(equalTo: nickNameStack.bottomAnchor, constant: 8).isActive = true
        // 不设置高度约束，让它根据内容自适应

        self.signLabel = signTextView
    }
    
    // 创建用户信息按钮
    // 【现代UI】: 可以调整颜色为灰色背景
    private func userInfoTagBtn(title: String, imageName: String? = nil) -> UIButton {
        let btn = UIButton(type: .system)
        if let imgName = imageName {
            btn.setImage(UIImage(named: imgName)?.withRenderingMode(.alwaysOriginal), for: .normal)
        }
        btn.setTitle(title, for: .normal)
        btn.setTitleColor(UIColor(white: 1, alpha: 0.7), for: .normal)
        // 【现代UI】: 可以修改颜色
        // btn.setTitleColor(UIColor(hex: "#777777"), for: .normal)
        
        btn.titleLabel?.font = .systemFont(ofSize: 12)
        btn.backgroundColor = UIColor(white: 1, alpha: 0.2)
        // 【现代UI】: 可以修改背景色
        // btn.backgroundColor = UIColor(hex: "#F5F5F5")
        
        btn.contentEdgeInsets = UIEdgeInsets(top: 4, left: 4, bottom: 4, right: 4)
        btn.cornerRadius = 2.5
        return btn
    }

    // 添加统计信息
    // 【现代UI】: 可以调整为居中显示的三列
    private func addStatisticInfo() {
        // 创建三个统计项
        let followingStatsView = StatsView()
        let likesStatsView = StatsView()
        let followersStatsView = StatsView()
        // 先用空数据，后续bind时动态赋值
        followingStatsView.configure(count: "-", title: "关注")
        likesStatsView.configure(count: "-", title: "获赞")
        followersStatsView.configure(count: "-", title: "粉丝")
        // 保存引用，供bind方法使用
        self.followingStatsView = followingStatsView
        self.likesStatsView = likesStatsView
        self.followersStatsView = followersStatsView
        let statsStackView = UIStackView(arrangedSubviews: [followingStatsView, likesStatsView, followersStatsView])
        statsStackView.axis = .horizontal
        statsStackView.distribution = .equalSpacing
        statsStackView.alignment = .center
        statsStackView.spacing = 46
        contentCardView.addSubview(statsStackView)
        statsStackView.snp.makeConstraints { make in
            make.top.equalTo(signLabel!.snp.bottom).offset(12) // 改为基于个性签名的底部
            make.centerX.equalTo(contentCardView)
            make.height.equalTo(50)
        }
        
        // 添加按钮容器
        let buttonsContainer = UIView()
        buttonsContainer.backgroundColor = .clear
        contentCardView.addSubview(buttonsContainer)
        
        // 设置按钮容器约束
        buttonsContainer.translatesAutoresizingMaskIntoConstraints = false
        buttonsContainer.centerXAnchor.constraint(equalTo: contentCardView.centerXAnchor).isActive = true
        buttonsContainer.topAnchor.constraint(equalTo: statsStackView.bottomAnchor, constant: 16).isActive = true
        buttonsContainer.heightAnchor.constraint(equalToConstant: 40).isActive = true
        // 移除按钮容器到contentCardView的底部约束
        // buttonsContainer.bottomAnchor.constraint(equalTo: contentCardView.bottomAnchor, constant: -16).isActive = true

        // 让contentCardView的底部基于按钮容器
        contentCardView.bottomAnchor.constraint(equalTo: buttonsContainer.bottomAnchor, constant: 16).isActive = true
        
        // 创建关注按钮
        followBtn = UIButton(type: .custom)
        followBtn.setTitle("关注", for: .normal)
        followBtn.setTitleColor(.white, for: .normal)
        followBtn.titleLabel?.font = UIFont.systemFont(ofSize: 13, weight: .medium)
        followBtn.layer.cornerRadius = 20
        followBtn.backgroundColor = .clear
        followBtn.addTarget(self, action: #selector(followAction), for: .touchUpInside)

        // 确保按钮可以接收触摸事件
        followBtn.isUserInteractionEnabled = true
        followBtn.isEnabled = true
        
        // 将关注按钮添加到容器并居中
        buttonsContainer.addSubview(followBtn)
        followBtn.translatesAutoresizingMaskIntoConstraints = false
        followBtn.centerXAnchor.constraint(equalTo: buttonsContainer.centerXAnchor).isActive = true
        followBtn.topAnchor.constraint(equalTo: buttonsContainer.topAnchor).isActive = true
        followBtn.bottomAnchor.constraint(equalTo: buttonsContainer.bottomAnchor).isActive = true
        followBtn.widthAnchor.constraint(equalToConstant: 96).isActive = true

        // 确保按钮容器也能接收触摸事件
        buttonsContainer.isUserInteractionEnabled = true

        // 将按钮提升到最前面，避免被其他视图遮挡
        buttonsContainer.bringSubviewToFront(followBtn)
        
        // 如果未来需要显示私信按钮，可在此处重新创建并添加
        
        // 为关注按钮添加渐变背景（在视图将显示时设置）
        self.layoutIfNeeded() // 确保按钮尺寸已经计算好
        addGradientToFollowButton()
    }

    // 为关注按钮添加渐变背景
    private func addGradientToFollowButton() {
        // 移除之前可能添加的渐变层
        followBtn.layer.sublayers?.forEach { layer in
            if layer is CAGradientLayer {
                layer.removeFromSuperlayer()
            }
        }
        
        // 创建新的渐变层
        let gradientLayer = CAGradientLayer()
        gradientLayer.colors = [
            UIColor(hex: "#FF8D36").cgColor,
            UIColor(hex: "#FF6236").cgColor,
            UIColor(hex: "#FF3B74").cgColor
        ]
        gradientLayer.startPoint = CGPoint(x: 0, y: 0)
        gradientLayer.endPoint = CGPoint(x: 1, y: 1)
        gradientLayer.locations = [0, 0.5, 1]
        gradientLayer.cornerRadius = 20
        gradientLayer.frame = followBtn.bounds
        
        // 插入渐变层到最底层
        followBtn.layer.insertSublayer(gradientLayer, at: 0)
    }
    
    // 更新layoutSubviews方法，只处理按钮渐变
    override func layoutSubviews() {
        super.layoutSubviews()
        
        // 更新关注按钮渐变层尺寸
        if let gradientLayer = followBtn?.layer.sublayers?.first as? CAGradientLayer {
            gradientLayer.frame = followBtn.bounds
        }
    }

    // 添加分段视图
    // 【现代UI】: 现在已经有圆角，可以保持不变
    private func addSegmentView() {
        segmentView = PageSegmentView()
        segmentView.backgroundColor = .white
        bgContainerView.addSubview(segmentView)
        segmentView.translatesAutoresizingMaskIntoConstraints = false
        segmentView.leftAnchor.constraint(equalTo: bgContainerView.leftAnchor).isActive = true
        segmentView.rightAnchor.constraint(equalTo: bgContainerView.rightAnchor).isActive = true
        segmentView.bottomAnchor.constraint(equalTo: bgContainerView.bottomAnchor).isActive = true
        segmentView.heightAnchor.constraint(equalToConstant: segmentViewHeight).isActive = true
        
        // 设置圆角 - 已经实现了圆角效果，可以保持不变
        segmentView.layer.cornerRadius = 15
        segmentView.layer.maskedCorners = [.layerMinXMinYCorner, .layerMaxXMinYCorner]
        segmentView.clipsToBounds = true
        
        // 【现代UI】: 设置top约束，与内容卡片之间留出空间
        segmentView.topAnchor.constraint(equalTo: contentCardView.bottomAnchor, constant: 20).isActive = true

        let bottomLine = UIView()
        bottomLine.backgroundColor = UIColor(white: 1, alpha: 0.2)
        bgContainerView.addSubview(bottomLine)
        bottomLine.translatesAutoresizingMaskIntoConstraints = false
        bottomLine.leftAnchor.constraint(equalTo: bgContainerView.leftAnchor).isActive = true
        bottomLine.bottomAnchor.constraint(equalTo: bgContainerView.bottomAnchor).isActive = true
        bottomLine.rightAnchor.constraint(equalTo: bgContainerView.rightAnchor).isActive = true
        bottomLine.heightAnchor.constraint(equalToConstant: 0.5).isActive = true
        
        // 【现代UI】: 可以考虑移除底部线条
    }

    // 背景图片动画效果
    func backgroundImageAnimation(offset: CGFloat) {
        let ratio = CGFloat(fabsf(Float(offset))) / width
        let height = (ratio * width) / 2
        
        // 应用变换到背景图 - 渐变遮罩作为子视图会自动跟随
        headerImage.transform = CGAffineTransform(scaleX: ratio + 1, y: ratio + 1)
            .concatenating(CGAffineTransform(translationX: 0, y: -height))
        
        // 确保渐变遮罩填满整个背景图且有额外边距
        if let overlayView = headerImage.subviews.first {
            // 获取变换后的背景图尺寸
            let transformedBounds = headerImage.bounds
            
            // 扩展遮罩比背景图稍大，确保完全覆盖
            let expandedFrame = CGRect(
                x: transformedBounds.origin.x - 20,
                y: transformedBounds.origin.y - 20,
                width: transformedBounds.width + 40,
                height: transformedBounds.height + 40
            )
            
            overlayView.frame = expandedFrame
            
            // 确保渐变层填满扩展后的遮罩视图
            if let gradientLayer = overlayView.layer.sublayers?.first as? CAGradientLayer {
                gradientLayer.frame = overlayView.bounds
            }
        }
    }

    // 确保关注按钮的交互性
    func ensureFollowButtonInteraction() {
        followBtn?.isUserInteractionEnabled = true
        followBtn?.isEnabled = true

        // 确保按钮在视图层级的最前面
        if let container = followBtn?.superview {
            container.bringSubviewToFront(followBtn!)
        }

        // 确保整个 contentCardView 的交互性
        contentCardView?.isUserInteractionEnabled = true

        print("关注按钮交互性检查完成 - isEnabled: \(followBtn?.isEnabled ?? false), isUserInteractionEnabled: \(followBtn?.isUserInteractionEnabled ?? false)")
    }

    // 新增：数据绑定方法
    func bind(_ data: PersonalHomeInfoData) {
        // 设置头像
        if !data.wxAvator.isEmpty, let url = URL(string: data.wxAvator) {
            avatar.kf.setImage(with: url, placeholder: UIImage(named: "default_avatar_test"))
        } else {
            avatar.image = UIImage(named: "default_avatar_test")
        }

        // 设置背景图：优先使用背景图，没有背景图时使用头像图
        if let backgroundImage = data.backgroundImage, !backgroundImage.isEmpty, let backgroundURL = URL(string: backgroundImage) {
            // 有背景图，使用背景图
            headerImage.kf.setImage(with: backgroundURL, placeholder: nil)
        } else if !data.wxAvator.isEmpty, let avatarURL = URL(string: data.wxAvator) {
            // 没有背景图但有头像，使用头像作为背景
            headerImage.kf.setImage(with: avatarURL, placeholder: nil)
        } else {
            // 既没有背景图也没有头像，使用默认图片
            headerImage.image = UIImage(named: "default_avatar_test")
        }
        
        // 昵称
        nickNameLabel?.text = data.displayNickName
        // 账号
        accountLabel?.text = "树小柒号：" + (data.customerAccount.isEmpty ? data.customerId : data.customerAccount)
        // 标签
        if let tagListStack = tagListStack as? UIStackView {
            // 清空现有标签
            tagListStack.arrangedSubviews.forEach { $0.removeFromSuperview() }
            
            // 使用labels数组作为标签源
            let labels = data.labels
            
            // 添加所有标签，最多显示3个
            if !labels.isEmpty {
                let displayLabels = labels.count > 3 ? Array(labels[0..<3]) : labels
                for label in displayLabels {
                    addTagLabel(label, to: tagListStack)
                }
            }
        }
        
        // 个性签名 - 解析@用户名
        if let personalitySign = data.personalitySign, !personalitySign.isEmpty {
            let parsedSignature = parseSignature(from: personalitySign, mapping: data.mentionedUser)
            if parsedSignature.isEmpty {
                signLabel?.text = "这个人很神秘，什么都没写~"
            } else {
                signLabel?.attributedText = attributedSignature(from: parsedSignature, mentionedUser: data.mentionedUser)
            }
        } else {
            signLabel?.text = "这个人很神秘，什么都没写~"
        }

        // 强制刷新个性签名的布局，确保多行文本正确显示
        if let signLabel = signLabel {
            // 确保文本容器设置正确
            signLabel.textContainer.maximumNumberOfLines = 0
            signLabel.textContainer.lineBreakMode = .byWordWrapping
            signLabel.isScrollEnabled = false

            // 强制重新计算内容大小
            signLabel.setNeedsLayout()
            signLabel.layoutIfNeeded()

            // 异步更新布局，确保在下一个运行循环中生效
            DispatchQueue.main.async {
                signLabel.setNeedsLayout()
                signLabel.layoutIfNeeded()
            }
        }
        // 统计信息
        followingStatsView?.configure(count: formatCount(data.followNumber), title: "关注")
        likesStatsView?.configure(count: formatCount(data.likeNumber), title: "获赞")
        followersStatsView?.configure(count: formatCount(data.fansNumber), title: "粉丝")
        // 关注按钮
        if let followBtn = self.followBtn {
            if data.follow {
                followBtn.setTitle("已关注", for: .normal)
                followBtn.setTitleColor(UIColor(hex: "#666666"), for: .normal)
                followBtn.layer.sublayers?.forEach { if $0 is CAGradientLayer { $0.removeFromSuperlayer() } }
                followBtn.backgroundColor = UIColor(hex: "#DDDDDD")
            } else {
                followBtn.setTitle("关注", for: .normal)
                followBtn.setTitleColor(.white, for: .normal)
                addGradientToFollowButton()
            }
        }
        // 关注/获赞/粉丝 segment 标题
        let worksTitle = "作品 " + formatCount(data.worksNumber)
        let likeTitle = "喜欢 " + formatCount(data.worksLikeNumber)
        let collectTitle = "收藏 " + formatCount(data.worksCollectNumber)
        segmentView.setSegmentTitles([worksTitle, likeTitle, collectTitle])

        // 确保关注按钮的交互性
        ensureFollowButtonInteraction()
    }
    
    // 添加单个标签的辅助方法
    private func addTagLabel(_ text: String, to stackView: UIView) {
        let tagLabel = TagPaddingLabel()
        tagLabel.text = text
        tagLabel.font = .systemFont(ofSize: 11, weight: .medium)
        tagLabel.textColor = UIColor.white
        // 使用统一的灰色背景
        tagLabel.backgroundColor = UIColor(hex: "#D5D5D5")
        tagLabel.layer.cornerRadius = 8
        tagLabel.layer.masksToBounds = true
        tagLabel.textAlignment = .center
        tagLabel.setContentHuggingPriority(.required, for: .horizontal)
        tagLabel.setContentCompressionResistancePriority(.required, for: .horizontal)
        tagLabel.translatesAutoresizingMaskIntoConstraints = false
        tagLabel.heightAnchor.constraint(equalToConstant: 16).isActive = true
        
        // 添加内边距
        let insets = UIEdgeInsets(top: 0, left: 8, bottom: 0, right: 8)
        tagLabel.textInsets = insets
        
        if let tagsContainer = stackView as? TagsContainerView {
            tagsContainer.addTagView(tagLabel)
        } else if let stackView = stackView as? UIStackView {
            stackView.addArrangedSubview(tagLabel)
        }
    }

    // 数字格式化方法
    private func formatCount(_ count: Int) -> String {
        if count >= 10000 {
            let value = Double(count) / 10000.0
            return String(format: "%.1f万", value)
        } else {
            return "\(count)"
        }
    }
}

// MARK: - 按钮点击事件

extension PageHeaderView {
    // 关注按钮点击事件
    @objc func followAction() {
        print("关注按钮被点击了") // 添加调试日志
        let isFollowing = followBtn.titleLabel?.text == "已关注"
        onFollowButtonTap?(isFollowing)
    }

    // 重写 hitTest 方法来调试触摸事件
    override func hitTest(_ point: CGPoint, with event: UIEvent?) -> UIView? {
        let hitView = super.hitTest(point, with: event)

        // 检查点击是否在关注按钮区域内
        if let followBtn = self.followBtn {
            let buttonFrame = followBtn.convert(followBtn.bounds, to: self)
            if buttonFrame.contains(point) {
                print("触摸点在关注按钮区域内，返回的视图: \(hitView?.description ?? "nil")")
                // 如果触摸在按钮区域内但返回的不是按钮，强制返回按钮
                if hitView != followBtn && followBtn.isUserInteractionEnabled {
                    print("强制返回关注按钮")
                    return followBtn
                }
            }
        }

        return hitView
    }

    // MARK: - 私密账号处理

    /// 隐藏统计数据（私密账号）
    func hideStatsForPrivateAccount() {
        // 隐藏segment标题中的数量
        segmentView.setSegmentTitles(["作品", "喜欢", "收藏"])
    }

    /// 显示统计数据（公开账号）
    func showStatsForPublicAccount() {
        // 如果有数据，重新设置带数量的标题
        if let data = getCurrentPersonalHomeInfoData() {
            let worksTitle = "作品 " + formatCount(data.worksNumber)
            let likeTitle = "喜欢 " + formatCount(data.worksLikeNumber)
            let collectTitle = "收藏 " + formatCount(data.worksCollectNumber)
            segmentView.setSegmentTitles([worksTitle, likeTitle, collectTitle])
        } else {
            // 没有数据时显示默认标题
            segmentView.setSegmentTitles(["作品", "喜欢", "收藏"])
        }
    }

    // 获取当前的个人主页数据（需要从父控制器获取）
    private func getCurrentPersonalHomeInfoData() -> PersonalHomeInfoData? {
        // 通过响应链查找PersonalHomepageViewController
        var responder: UIResponder? = self
        while responder != nil {
            if let personalVC = responder as? PersonalHomepageViewController {
                return personalVC.personHomeInfo?.data
            }
            responder = responder?.next
        }
        return nil
    }
}

// 在文件末尾添加PaddingLabel类
class TagPaddingLabel: UILabel {
    var textInsets = UIEdgeInsets.zero
    
    override func drawText(in rect: CGRect) {
        super.drawText(in: rect.inset(by: textInsets))
    }
    
    override var intrinsicContentSize: CGSize {
        let size = super.intrinsicContentSize
        return CGSize(width: size.width + textInsets.left + textInsets.right,
                      height: size.height + textInsets.top + textInsets.bottom)
    }
}

// 添加标签流式布局容器
class TagsContainerView: UIStackView {
    var tagSpacing: CGFloat = 8
    var lineSpacing: CGFloat = 6
    
    private var currentRowStack: UIStackView?
    private var currentRowWidth: CGFloat = 0
    private var maxWidth: CGFloat = 0
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupView()
    }
    
    required init(coder: NSCoder) {
        super.init(coder: coder)
        setupView()
    }
    
    private func setupView() {
        axis = .vertical
        distribution = .equalSpacing
        alignment = .center
        spacing = lineSpacing
    }
    
    override func layoutSubviews() {
        super.layoutSubviews()
        
        // 如果容器宽度发生变化，需要重新布局标签
        if maxWidth != bounds.width && bounds.width > 0 {
            maxWidth = bounds.width
            
            // 收集所有标签
            var allTags: [UIView] = []
            arrangedSubviews.forEach { rowStack in
                if let stack = rowStack as? UIStackView {
                    stack.arrangedSubviews.forEach { tag in
                        allTags.append(tag)
                    }
                }
            }
            
            // 移除所有行
            removeAllArrangedSubviews()
            
            // 重新添加标签
            allTags.forEach { tag in
                addTagView(tag)
            }
        }
    }
    
    override func addArrangedSubview(_ view: UIView) {
        // 不直接添加到自身，而是管理行堆栈
        addTagView(view)
    }
    
    func addTagView(_ tagView: UIView) {
        // 获取标签宽度
        let tagWidth = tagView.intrinsicContentSize.width
        
        // 如果当前行不存在或者添加这个标签会超出容器宽度，创建新行
        if currentRowStack == nil || (currentRowWidth + tagWidth + tagSpacing > bounds.width && currentRowWidth > 0) {
            createNewRow()
        }
        
        // 添加标签到当前行
        currentRowStack?.addArrangedSubview(tagView)
        currentRowWidth += tagWidth + tagSpacing
    }
    
    private func createNewRow() {
        let rowStack = UIStackView()
        rowStack.axis = .horizontal
        rowStack.spacing = tagSpacing
        rowStack.alignment = alignment
        rowStack.distribution = .fill
        super.addArrangedSubview(rowStack)
        
        currentRowStack = rowStack
        currentRowWidth = 0
    }
    
    func removeAllArrangedSubviews() {
        // 移除所有行
        arrangedSubviews.forEach { rowStack in
            rowStack.removeFromSuperview()
        }
        
        currentRowStack = nil
        currentRowWidth = 0
    }
}

// MARK: - PageHeaderView 个性签名解析扩展
extension PageHeaderView {

    /// 将个性签名中的"@用户名"部分高亮显示并添加点击链接
    /// - Parameters:
    ///   - signature: 用户个性签名
    ///   - mentionedUser: 用户ID到昵称的映射
    /// - Returns: 处理后的富文本
    private func attributedSignature(from signature: String, mentionedUser: [String: String]?) -> NSAttributedString {
        let attributed = NSMutableAttributedString(string: signature)
        // 默认颜色
        let fullRange = NSRange(location: 0, length: attributed.length)
        attributed.addAttribute(.foregroundColor, value: UIColor(hex: "#777777"), range: fullRange)
        attributed.addAttribute(.font, value: UIFont.systemFont(ofSize: 12), range: fullRange)

        // 手动解析@用户名，确保精确匹配
        let nsString = signature as NSString
        var searchRange = NSRange(location: 0, length: nsString.length)

        while searchRange.location < nsString.length {
            let atRange = nsString.range(of: "@", options: [], range: searchRange)
            if atRange.location == NSNotFound {
                break
            }

            // 找到@符号，开始解析用户名
            var usernameEnd = atRange.location + atRange.length
            let maxEnd = min(nsString.length, usernameEnd + 50) // 限制用户名最大长度

            // 查找用户名的结束位置
            while usernameEnd < maxEnd {
                let char = nsString.character(at: usernameEnd)
                let isValidChar = (char >= 48 && char <= 57) ||  // 0-9
                                 (char >= 65 && char <= 90) ||   // A-Z
                                 (char >= 97 && char <= 122) ||  // a-z
                                 char == 95 ||                   // _
                                 char == 45 ||                   // -
                                 (char >= 0x4e00 && char <= 0x9fa5) // 中文字符

                if !isValidChar {
                    break
                }
                usernameEnd += 1
            }

            // 如果找到了有效的用户名（长度大于1，即除了@符号还有其他字符）
            if usernameEnd > atRange.location + 1 {
                let mentionRange = NSRange(location: atRange.location, length: usernameEnd - atRange.location)

                // 高亮显示
                attributed.addAttribute(.foregroundColor, value: UIColor(hex: "#4478F5"), range: mentionRange)

                // 获取@用户名文本
                let mentionText = nsString.substring(with: mentionRange)
                let userName = String(mentionText.dropFirst()) // 去掉@符号

                // 查找对应的用户ID
                if let mentionedUser = mentionedUser {
                    for (userId, nickName) in mentionedUser {
                        if userName == nickName {
                            // 添加链接属性，使用用户ID作为链接
                            attributed.addAttribute(.link, value: "user://\(userId)", range: mentionRange)
                            break
                        }
                    }
                }
            }

            // 继续搜索下一个@符号
            searchRange.location = usernameEnd
            searchRange.length = nsString.length - searchRange.location
        }

        return attributed
    }

    /// 解析 snapshot，将 @id 替换为用户昵称，并去除多余的反斜杠
    private func parseSignature(from snapshot: String, mapping: [String: String]?) -> String {
        var result = snapshot
        if result.isEmpty { return "" }
        // 去除转义反斜杠
        result = result.replacingOccurrences(of: "\\", with: "")
        if let map = mapping {
            for (id, name) in map {
                result = result.replacingOccurrences(of: "@\(id)", with: "@\(name)")
            }
        }
        return result
    }
}

// MARK: - UITextViewDelegate
extension PageHeaderView {

    /// 处理链接点击事件
    func textView(_ textView: UITextView, shouldInteractWith URL: URL, in characterRange: NSRange, interaction: UITextItemInteraction) -> Bool {
        if URL.scheme == "user" {
            let userId = URL.host ?? ""
            if !userId.isEmpty {
                // 通过通知或回调的方式通知控制器跳转到用户主页
                NotificationCenter.default.post(name: NSNotification.Name("NavigateToUserProfile"), object: userId)
            }
        }
        return false // 返回false阻止默认行为
    }
}
